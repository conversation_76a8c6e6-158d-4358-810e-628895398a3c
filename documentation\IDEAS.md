# LuminariMUD Ideas List

This file contains player-submitted ideas for game improvements and new features. Bug reports and typos are tracked separately.

## User Interface Improvements

### Shop Marking on Map
- **Description**: Display shops as $ symbol on ASCII minimap for better visibility
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 12)

### Keyring Wearable Item
- **Description**: Make keyrings a wearable item (belt slot) that allows keys to be used while stored
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 13), <PERSON><PERSON> (Level 12)

### In-Character Communication
- **Description**: Telepathy item system in unique slot, restringable and craftable for IC communication
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 13)

### Charmee Affects Display
- **Description**: Command to check what spells/buffs are active on charmees
- **Submitted by**: Plixid (Level 8)

### Location Coordinates on Prompt
- **Description**: Add room coordinates to prompt display
- **Submitted by**: Age (Level 10)

### Combined Affects/Score Screen
- **Description**: <PERSON><PERSON><PERSON> affects information into score screen for convenience
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 7)

### Help in Study Mode
- **Description**: Allow help commands within study screens (skills, feats, selections)
- **Submitted by**: Metvagen (Level 21)

### Gain Command with Arguments
- **Description**: Allow "gain 19" to gain multiple levels at once after respec
- **Submitted by**: Metvagen (Level 3)

### Help in Character Creation
- **Description**: Enable help files during character creation process
- **Submitted by**: Zusuk (Level 34)

### Colorize Room Names
- **Description**: Add color to room names for better visibility
- **Submitted by**: Delax (Level 1)

### Group Chat History
- **Description**: Add group chat to "history all" command
- **Submitted by**: Brondo (Level 30)

### Defensive Stance Notification
- **Description**: Alert when stalwart defender's defensive stance expires
- **Submitted by**: Brondo (Level 30)

### Compare Command
- **Description**: Add command to compare weapons and armor stats
- **Submitted by**: Darowin (Level 3)

## Crafting System

### Stop/Cancel Crafting Command
- **Description**: Command to interrupt crafting process
- **Submitted by**: Dagmar (Level 1)

### Crafting Skill XP Display
- **Description**: Show current XP and XP needed for next rank in crafting skills
- **Submitted by**: Ostvel (Level 27)

### Early Area Crafting Molds
- **Description**: Add armor and cloth molds to Ashenport crafting store
- **Submitted by**: Mendev (Level 20)

### Restring Enhancements
- **Description**: Allow modification of description and material in addition to name
- **Submitted by**: Metvagen (Level 19)

### Periodic Crafting Bonuses
- **Description**: Crafters gain XP when their crafted items are used in combat
- **Submitted by**: Gicker (Level 34), originally by Tollymore

### Crafting Missions from Bazaar
- **Description**: Create crafting missions when players order from Bazaar, paying quest points
- **Submitted by**: Tollymore (Level 8)

### Crafting Recipe Adventure Loop
- **Description**: Recipe system with key components from spawned crafting nodes with guardians
- **Submitted by**: Tollymore (Level 8)

### Weapon Type Conversion
- **Description**: Crafting method to change weapon form without losing enchantments
- **Submitted by**: Murdoch (Level 13)

## Combat & Class Features

### Class Feats Display for Wizards
- **Description**: Show wizard bonus feats every 5 levels in class feats display
- **Submitted by**: Ortallus (Level 4)

### Berserker Acrobatics
- **Description**: Add acrobatics as class skill for berserkers (Pathfinder alignment)
- **Submitted by**: Mendev (Level 23)

### Naming Companions
- **Description**: Optional command to assign names to followers/summons/companions
- **Submitted by**: Valafar (Level 9)

### Greater Feint Feat
- **Description**: New feat allowing feint as move action
- **Submitted by**: Yure (Level 3)

### Zombie Bite Attacks
- **Description**: Add bite and occasional blunt attacks to animated dead zombies
- **Submitted by**: Metvagen (Level 12)

### Rogue Poison Creation
- **Description**: Weak, long-lasting poison creation for rogues without expensive materials
- **Submitted by**: Serul (Level 26)

### Summon Shadow Teamwork
- **Description**: Grant shadow any teamwork feats the caster has
- **Submitted by**: Raiko (Level 15)

### Non-Druid Wildshape Forms
- **Description**: Allow viewing wildshape forms for polymorph self spell
- **Submitted by**: Ilzude (Level 23)

### Healing Wave Spell
- **Description**: Multi-round healing spell (100-200 HP per round for 3-5 rounds)
- **Submitted by**: Melow (Level 30)

### Damage Display Toggle
- **Description**: Toggle to view damage from other players/pets
- **Submitted by**: Brondo (Level 30)

### Careful With Pets Default
- **Description**: Make "careful with pets" on by default
- **Submitted by**: Malicor (Level 23)

### Circle/Backstab on Blind/Paralyzed
- **Description**: Allow circle when enemy is blind/paralyzed even if tanking
- **Submitted by**: Murdoch (Level 30)

### Class-Specific AI Casting
- **Description**: Mob casting tables by class for more realistic spellcasting
- **Submitted by**: Dudris (Level 31)

### Circle Combat Initiation
- **Description**: Allow circle command to engage combat when someone else is tanking
- **Submitted by**: Badase (Level 30)

### Buffself Stand Command
- **Description**: Auto-stand when using "buffself perform" command
- **Submitted by**: Lyllian (Level 7)

## Race & Character Options

### Illithid Race
- **Description**: Mind flayer race for high account XP cost (10k-30k)
- **Submitted by**: Rinne (Level 12)

### Lich Downsides
- **Description**: Balance lich with restrictions (no food/drink, forced CE alignment)
- **Submitted by**: Brondo (Level 30)

### Magus Class
- **Description**: New class to supplement spellsword/eldritch knight
- **Submitted by**: Sedron (Level 29)

## Game Mechanics

### Food/Water Rest Bonus
- **Description**: HP recovery bonus while resting with food/water
- **Submitted by**: Mendev (Level 17)

### Temple Association
- **Description**: Temple affiliation system filling God slot, providing quest chains
- **Submitted by**: Arvaunshae (Level 17)

### PSP on GUI
- **Description**: Add psionic power points to GUI display
- **Submitted by**: Zusuk (Level 34)

### Auto-Sacrifice Toggle
- **Description**: Auto-grab items when sacrificing corpses
- **Submitted by**: Metvagen (Level 20)

### Message Speed Control
- **Description**: Slow down NPC message delivery for readability
- **Submitted by**: Delax (Level 1)

### Screen Reader Support
- **Description**: Better integration with screen reader detection
- **Submitted by**: Harlon (Level 3)

### Mass Enhance Priority
- **Description**: Mass enhance should override or stack with individual stat buffs
- **Submitted by**: Serulina (Level 22)

### Unlockable Feats List
- **Description**: Show locked feats and their requirements
- **Submitted by**: Dasvel (Level 28)

### Independent Feat Changes
- **Description**: Allow feat changes without full respec
- **Submitted by**: Yaran (Level 9)

### Read During Crafting
- **Description**: Allow reading news/motd while crafting
- **Submitted by**: Kormundrad (Level 3)

### Store Food/Drink
- **Description**: Add food/drink enhancements to store feature
- **Submitted by**: Brondo (Level 30)

### Poison Vial Cleanup
- **Description**: Empty poison vials should disappear after last use
- **Submitted by**: Badase (Level 30)

### Locate Object Filter
- **Description**: Exclude items in player houses from locate object
- **Submitted by**: Diel (Level 30)

### Mission Location Mapping
- **Description**: Only create missions to mapped locations
- **Submitted by**: Tanaka (Level 13)

### Save Command Hint
- **Description**: Add save command instruction to tutorial/hints
- **Submitted by**: Fish (Level 1)

### Quest Item Drop Command
- **Description**: Command for quest mobs to drop inventory items without killing
- **Submitted by**: Arithon (Level 30)

### Feat Point Documentation
- **Description**: Explain different types of feat points and their uses
- **Submitted by**: Neurrone (Level 21)

### Polymorph Self Clarification
- **Description**: Help file should mention no race feats when polymorphed
- **Submitted by**: Neurrone (Level 22)

### Shifter Dragon Buffs
- **Description**: Increase dragon form breath weapon and spell damage significantly
- **Submitted by**: Ogoun (Level 30)

### Two-Axis Alignment
- **Description**: Separate Law/Chaos and Good/Evil scales
- **Submitted by**: Ogoun (Level 30)

## Quality of Life

### Unlock Door Message
- **Description**: Change "*click*" to "You unlock the door"
- **Submitted by**: Delax (Level 1)

### Colorful OK Messages
- **Description**: Replace generic "okay" with more colorful responses
- **Submitted by**: Zusuk (Level 34)

### Harvesting Depletion Message
- **Description**: Show "depleted" at end of harvest process instead of warning
- **Submitted by**: Variel (Level 2)

### Beginning Journey for Blind
- **Description**: Add coordinates of half-orc camp to quest description
- **Submitted by**: Ozrim (Level 3)

### Psionic Power Conservation
- **Description**: Don't consume PSP when power can't be manifested
- **Submitted by**: Fyre (Level 3)

### Intimidate Duration Info
- **Description**: Add duration information to intimidate help file
- **Submitted by**: Mendev (Level 10)

### Staff Helper for Queues
- **Description**: Add field to categorize bug/typo/idea submissions by type
- **Submitted by**: Jordan (Level 31)

### Spell Class/Circle Info
- **Description**: Add class and circle information to spell help files
- **Submitted by**: Murdoch (Level 30)

### Gear Splitting System
- **Description**: Dice roll or bidding system for fair loot distribution
- **Submitted by**: Lamix (Level 30)

### Mosswood Elder Directions
- **Description**: Have "ask elder ashenport" give directions to Ashenport
- **Submitted by**: Mddljeu (Level 2)

## New Spells & Abilities

### Dimension Door
- **Description**: Implement dimension door spell
- **Submitted by**: Gargar (Level 22)

### Dispel Invisibility
- **Description**: Spell to remove invisibility from equipment
- **Submitted by**: Melaw (Level 30)

### Area Wall Spells
- **Description**: Prismatic cube or force cube area denial spells
- **Submitted by**: Melaw (Level 30)

### Healer Class Unlock
- **Description**: Prestige class with healing bonuses and faster memorization
- **Submitted by**: Melaw (Level 30)

## NPC & World Interaction

### Ashenport Tourist Quests
- **Description**: Small escort missions for lost tourists in city
- **Submitted by**: Metvagen (Level 12)

### Smoking System
- **Description**: Immersive smoking system for roleplay
- **Submitted by**: Arvaunshae (Level 17)

## Accessibility

### Symbol Alternatives
- **Description**: Use letters/words instead of punctuation for screen readers
- **Submitted by**: Dranulous (Level 2)

### Plane Travel Effect
- **Description**: Add humorous message for maintained velocity after recall from falling
- **Submitted by**: Iliri (Level 26)